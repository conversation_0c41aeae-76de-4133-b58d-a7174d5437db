2025/05/27-16:53:50.267 1f203 Creating DB /Users/<USER>/Documents/augment-projects/WaAgent/whatsapp-ai-agent/.wwebjs_auth/session/Default/IndexedDB/https_web.whatsapp.com_0.indexeddb.leveldb since it was missing.
2025/05/27-16:53:50.279 1f203 Reusing MANIFEST /Users/<USER>/Documents/augment-projects/WaAgent/whatsapp-ai-agent/.wwebjs_auth/session/Default/IndexedDB/https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/05/27-16:53:50.590 1f707 Level-0 table #5: started
2025/05/27-16:53:50.598 1f707 Level-0 table #5: 10232 bytes OK
2025/05/27-16:53:50.613 1f707 Delete type=0 #3
2025/05/27-16:53:50.613 1f707 Manual compaction at level-0 from '\x00\x02\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x03\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.617 a503 Level-0 table #7: started
2025/05/27-16:53:50.620 a503 Level-0 table #7: 956 bytes OK
2025/05/27-16:53:50.631 a503 Delete type=0 #4
2025/05/27-16:53:50.631 a503 Manual compaction at level-0 from '\x00\x03\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x04\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.631 a503 Manual compaction at level-1 from '\x00\x03\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x04\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0d\x00\x00\x04' @ 651 : 1
2025/05/27-16:53:50.631 a503 Compacting 1@1 + 1@2 files
2025/05/27-16:53:50.643 a503 Generated table #8@1: 266 keys, 6288 bytes
2025/05/27-16:53:50.643 a503 Compacted 1@1 + 1@2 files => 6288 bytes
2025/05/27-16:53:50.655 a503 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/27-16:53:50.655 a503 Delete type=2 #7
2025/05/27-16:53:50.655 a503 Manual compaction at level-1 from '\x00\x0d\x00\x00\x04' @ 651 : 1 .. '\x00\x04\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.659 13113 Level-0 table #10: started
2025/05/27-16:53:50.662 13113 Level-0 table #10: 1208 bytes OK
2025/05/27-16:53:50.669 13113 Delete type=0 #6
2025/05/27-16:53:50.669 13113 Delete type=2 #5
2025/05/27-16:53:50.669 13113 Manual compaction at level-0 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.669 13113 Manual compaction at level-1 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at '\x00\x04\x00\x00\x05' @ 748 : 0
2025/05/27-16:53:50.669 13113 Compacting 1@1 + 1@2 files
2025/05/27-16:53:50.673 13113 Generated table #11@1: 281 keys, 6504 bytes
2025/05/27-16:53:50.673 13113 Compacted 1@1 + 1@2 files => 6504 bytes
2025/05/27-16:53:50.681 13113 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/27-16:53:50.681 13113 Delete type=2 #10
2025/05/27-16:53:50.681 13113 Manual compaction at level-1 from '\x00\x04\x00\x00\x05' @ 748 : 0 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.685 13803 Level-0 table #13: started
2025/05/27-16:53:50.689 13803 Level-0 table #13: 577 bytes OK
2025/05/27-16:53:50.701 13803 Delete type=2 #8
2025/05/27-16:53:50.701 13803 Delete type=0 #9
2025/05/27-16:53:50.701 13803 Manual compaction at level-0 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.701 13803 Manual compaction at level-1 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0e\x00\x00\x05' @ 762 : 1
2025/05/27-16:53:50.701 13803 Compacting 1@1 + 1@2 files
2025/05/27-16:53:50.705 13803 Generated table #14@1: 286 keys, 6511 bytes
2025/05/27-16:53:50.705 13803 Compacted 1@1 + 1@2 files => 6511 bytes
2025/05/27-16:53:50.712 13803 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/27-16:53:50.712 13803 Delete type=2 #13
2025/05/27-16:53:50.712 13803 Manual compaction at level-1 from '\x00\x0e\x00\x00\x05' @ 762 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.712 13703 Level-0 table #16: started
2025/05/27-16:53:50.715 13703 Level-0 table #16: 863 bytes OK
2025/05/27-16:53:50.722 13703 Delete type=0 #12
2025/05/27-16:53:50.722 13703 Delete type=2 #11
2025/05/27-16:53:50.722 13703 Manual compaction at level-0 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.722 a503 Manual compaction at level-1 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0e\x00\x00\xc8\x04\x00k\x00e\x00y\x00s' @ 781 : 1
2025/05/27-16:53:50.722 a503 Compacting 1@1 + 1@2 files
2025/05/27-16:53:50.726 a503 Generated table #17@1: 321 keys, 6969 bytes
2025/05/27-16:53:50.726 a503 Compacted 1@1 + 1@2 files => 6969 bytes
2025/05/27-16:53:50.737 a503 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/27-16:53:50.737 a503 Delete type=2 #16
2025/05/27-16:53:50.737 13703 Manual compaction at level-1 from '\x00\x0e\x00\x00\xc8\x04\x00k\x00e\x00y\x00s' @ 781 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.737 13703 Level-0 table #19: started
2025/05/27-16:53:50.741 13703 Level-0 table #19: 1657 bytes OK
2025/05/27-16:53:50.752 13703 Delete type=0 #15
2025/05/27-16:53:50.752 13703 Delete type=2 #14
2025/05/27-16:53:50.758 13703 Manual compaction at level-0 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.758 13703 Manual compaction at level-1 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0f\x00\x00\xc8\x11\x00s\x00i\x00g\x00n\x00a\x00l\x00-\x00m\x00e\x00t\x00a\x00-\x00s\x00t\x00o\x00r\x00e' @ 877 : 1
2025/05/27-16:53:50.758 13703 Compacting 1@1 + 1@2 files
2025/05/27-16:53:50.768 13703 Generated table #20@1: 373 keys, 8078 bytes
2025/05/27-16:53:50.768 13703 Compacted 1@1 + 1@2 files => 8078 bytes
2025/05/27-16:53:50.777 13703 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/27-16:53:50.777 13703 Delete type=2 #19
2025/05/27-16:53:50.777 13703 Manual compaction at level-1 from '\x00\x0f\x00\x00\xc8\x11\x00s\x00i\x00g\x00n\x00a\x00l\x00-\x00m\x00e\x00t\x00a\x00-\x00s\x00t\x00o\x00r\x00e' @ 877 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.778 a503 Level-0 table #22: started
2025/05/27-16:53:50.782 a503 Level-0 table #22: 2135 bytes OK
2025/05/27-16:53:50.789 a503 Delete type=2 #17
2025/05/27-16:53:50.789 a503 Delete type=0 #18
2025/05/27-16:53:50.789 a503 Manual compaction at level-0 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.789 a503 Manual compaction at level-1 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at '\x00\x10\x00\x00\x05' @ 1016 : 1
2025/05/27-16:53:50.789 a503 Compacting 1@1 + 1@2 files
2025/05/27-16:53:50.794 a503 Generated table #23@1: 486 keys, 9801 bytes
2025/05/27-16:53:50.794 a503 Compacted 1@1 + 1@2 files => 9801 bytes
2025/05/27-16:53:50.802 a503 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/27-16:53:50.802 a503 Delete type=2 #22
2025/05/27-16:53:50.802 a503 Manual compaction at level-1 from '\x00\x10\x00\x00\x05' @ 1016 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.803 13113 Level-0 table #25: started
2025/05/27-16:53:50.806 13113 Level-0 table #25: 251 bytes OK
2025/05/27-16:53:50.813 13113 Delete type=2 #20
2025/05/27-16:53:50.813 13113 Delete type=0 #21
2025/05/27-16:53:50.814 13113 Manual compaction at level-0 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.814 13113 Manual compaction at level-1 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0c\x00\x00\x05' @ 1036 : 0
2025/05/27-16:53:50.814 13113 Compacting 1@1 + 1@2 files
2025/05/27-16:53:50.818 13113 Generated table #26@1: 480 keys, 9749 bytes
2025/05/27-16:53:50.818 13113 Compacted 1@1 + 1@2 files => 9749 bytes
2025/05/27-16:53:50.825 13113 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/27-16:53:50.825 13113 Delete type=2 #25
2025/05/27-16:53:50.825 13113 Manual compaction at level-1 from '\x00\x0c\x00\x00\x05' @ 1036 : 0 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.826 13803 Level-0 table #28: started
2025/05/27-16:53:50.830 13803 Level-0 table #28: 249 bytes OK
2025/05/27-16:53:50.838 13803 Delete type=2 #23
2025/05/27-16:53:50.838 13803 Delete type=0 #24
2025/05/27-16:53:50.838 13803 Manual compaction at level-0 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.838 13803 Manual compaction at level-1 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0a\x00\x00\x05' @ 1042 : 0
2025/05/27-16:53:50.839 13803 Compacting 1@1 + 1@2 files
2025/05/27-16:53:50.843 13803 Generated table #29@1: 474 keys, 9708 bytes
2025/05/27-16:53:50.843 13803 Compacted 1@1 + 1@2 files => 9708 bytes
2025/05/27-16:53:50.851 13803 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/27-16:53:50.851 13803 Delete type=2 #28
2025/05/27-16:53:50.851 13803 Manual compaction at level-1 from '\x00\x0a\x00\x00\x05' @ 1042 : 0 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.851 13113 Level-0 table #31: started
2025/05/27-16:53:50.854 13113 Level-0 table #31: 249 bytes OK
2025/05/27-16:53:50.862 13113 Delete type=2 #26
2025/05/27-16:53:50.862 13113 Delete type=0 #27
2025/05/27-16:53:50.862 13113 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.862 13113 Manual compaction at level-1 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0b\x00\x00\x05' @ 1048 : 0
2025/05/27-16:53:50.862 13113 Compacting 1@1 + 1@2 files
2025/05/27-16:53:50.866 13113 Generated table #32@1: 468 keys, 9480 bytes
2025/05/27-16:53:50.866 13113 Compacted 1@1 + 1@2 files => 9480 bytes
2025/05/27-16:53:50.874 13113 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/27-16:53:50.874 13113 Delete type=2 #31
2025/05/27-16:53:50.874 13703 Manual compaction at level-1 from '\x00\x0b\x00\x00\x05' @ 1048 : 0 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.874 13803 Level-0 table #34: started
2025/05/27-16:53:50.878 13803 Level-0 table #34: 249 bytes OK
2025/05/27-16:53:50.885 13803 Delete type=2 #29
2025/05/27-16:53:50.885 13803 Delete type=0 #30
2025/05/27-16:53:50.885 13803 Manual compaction at level-0 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.885 13803 Manual compaction at level-1 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at '\x00\x09\x00\x00\x05' @ 1054 : 0
2025/05/27-16:53:50.885 13803 Compacting 1@1 + 1@2 files
2025/05/27-16:53:50.890 13803 Generated table #35@1: 462 keys, 9302 bytes
2025/05/27-16:53:50.890 13803 Compacted 1@1 + 1@2 files => 9302 bytes
2025/05/27-16:53:50.898 13803 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/27-16:53:50.898 13803 Delete type=2 #34
2025/05/27-16:53:50.898 13803 Manual compaction at level-1 from '\x00\x09\x00\x00\x05' @ 1054 : 0 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.899 13113 Level-0 table #37: started
2025/05/27-16:53:50.903 13113 Level-0 table #37: 1595 bytes OK
2025/05/27-16:53:50.910 13113 Delete type=2 #32
2025/05/27-16:53:50.910 13113 Delete type=0 #33
2025/05/27-16:53:50.910 13113 Manual compaction at level-0 from '\x00\x10\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/05/27-16:53:50.910 13113 Manual compaction at level-1 from '\x00\x10\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at '\x00\x10\x00\x00\x05' @ 1224 : 0
2025/05/27-16:53:50.910 13113 Compacting 1@1 + 1@2 files
2025/05/27-16:53:50.915 13113 Generated table #38@1: 292 keys, 6111 bytes
2025/05/27-16:53:50.915 13113 Compacted 1@1 + 1@2 files => 6111 bytes
2025/05/27-16:53:50.923 13113 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/05/27-16:53:50.923 13113 Delete type=2 #37
2025/05/27-16:53:50.923 13113 Manual compaction at level-1 from '\x00\x10\x00\x00\x05' @ 1224 : 0 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at (end)
