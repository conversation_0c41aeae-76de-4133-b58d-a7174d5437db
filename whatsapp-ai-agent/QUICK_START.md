# 🚀 Quick Start Guide - WhatsApp AI Agent

## 📋 Prerequisites

Before starting, ensure you have:
- ✅ Node.js 16+ installed
- ✅ OpenAI API key ([Get one here](https://platform.openai.com/api-keys))
- ✅ WhatsApp account for scanning QR code
- ✅ Terminal/Command prompt access

---

## 🎯 Option 1: Automated Setup (Recommended)

### 1. Download and Run Setup Script
```bash
# Make the setup script executable
chmod +x setup-enhanced.sh

# Run the automated setup
./setup-enhanced.sh
```

### 2. Configure Environment Variables
```bash
# Edit .env file
nano .env

# Add your OpenAI API key:
OPENAI_API_KEY=sk-your-actual-api-key-here
```

### 3. Start the Bot
```bash
npm start
```

### 4. Scan QR Code
- A QR code will appear in your terminal
- Open WhatsApp on your phone
- Go to Settings > Linked Devices > Link a Device
- Scan the QR code

---

## 🛠️ Option 2: Manual Setup

### 1. Create Project Structure
```bash
mkdir whatsapp-ai-agent
cd whatsapp-ai-agent
mkdir -p src/{handlers,services,utils} temp logs
```

### 2. Copy Files
Copy each file to its proper location:
- Main bot code → `src/index.js`
- Shopping handler → `src/handlers/shopping.js`
- YouTube handler → `src/handlers/youtube.js`
- Assignment handler → `src/handlers/assignment.js`
- Flight handler → `src/handlers/flight.js`
- Hotel handler → `src/handlers/hotel.js`

### 3. Install Dependencies
```bash
npm install express whatsapp-web.js qrcode-terminal openai dotenv pdf-lib ytdl-core puppeteer winston axios node-cron
```

### 4. Configure Environment Variables
```bash
# Create .env file
cp .env.example .env

# Edit with your API keys
nano .env
```

### 5. Run the Bot
```bash
npm start
```

---

## 📝 Adding Advanced Features

Once your basic bot is working, enhance it with advanced features:

### 1. Create Advanced Services
```bash
# Create advanced features file
touch src/services/advanced-features.js
```

### 2. Import in Main File
```javascript
// In src/index.js
const { 
    PriceMonitor, 
    OrderTracker,
    YouTubeAdvanced 
} = require('./services/advanced-features');

// Initialize advanced features
const priceMonitor = new PriceMonitor();
const orderTracker = new OrderTracker();
```

### 3. Add Database Support (Optional)
```bash
# For persistent data storage
npm install sqlite3 sequelize
```

---

## 🔧 Troubleshooting

### Common Issues & Solutions:

#### ❌ "Cannot find module" Error
```bash
# Install missing module
npm install [missing-module-name]

# Or reinstall all dependencies
rm -rf node_modules package-lock.json
npm install
```

#### ❌ QR Code Not Showing
```bash
# Install Chromium (Linux)
sudo apt-get install chromium-browser

# Or add to puppeteer config in src/index.js:
const whatsapp = new Client({
    authStrategy: new LocalAuth(),
    puppeteer: {
        headless: true,
        args: ['--no-sandbox'],
        executablePath: '/usr/bin/chromium-browser'  // Add this line
    }
});
```

#### ❌ OpenAI API Errors
- ✅ Check your API key in `.env` file
- ✅ Ensure you have credits in your OpenAI account
- ✅ Verify API key format: `sk-...`

#### ❌ WhatsApp Session Issues
```bash
# Delete session data and restart
rm -rf .wwebjs_auth
npm start
# Scan QR code again
```

#### ❌ Permission Errors
```bash
# Fix file permissions
chmod +x setup-enhanced.sh
sudo chown -R $USER:$USER .
```

---

## 🎯 Testing Your Bot

### Basic Tests:
1. **Hello Test:**
   - You: `Hello`
   - Bot: `Hi! I'm your AI assistant...`

2. **Shopping Test:**
   - You: `Buy iPhone under $800`
   - Bot: `🔍 Searching for products...`

3. **YouTube Test:**
   - You: `Summarize https://youtube.com/watch?v=dQw4w9WgXcQ`
   - Bot: `📺 Analyzing video...`

4. **Assignment Test:**
   - You: [Send assignment photo]
   - Bot: `📚 Analyzing your assignment...`

### Advanced Tests:
- Test session management with multi-step conversations
- Try different product searches
- Upload various assignment types
- Test error handling with invalid inputs

---

## 📈 Next Steps

### Development Workflow:
1. **Start Simple** - Get basic messaging working first
2. **Add Features Incrementally** - Don't implement everything at once
3. **Test Thoroughly** - Each feature should work before moving to next
4. **Monitor Logs** - Check `logs/combined.log` for issues
5. **Iterate** - Improve based on user feedback

### Production Deployment:
```bash
# Using Docker
docker-compose up -d

# Using PM2
npm install -g pm2
pm2 start src/index.js --name "whatsapp-ai-agent"
```

### Scaling Considerations:
- Add Redis for session management
- Implement database for user data
- Set up monitoring and alerts
- Add rate limiting
- Implement user authentication

---

## 🆘 Getting Help

- 📖 Check the main [README.md](README.md) for detailed documentation
- 🐛 Review logs in `logs/` directory for error details
- 💬 Test with simple messages first before complex features
- 🔄 Restart the bot if you encounter persistent issues

---

## 🎉 Success!

If you see this message, your bot is working:
```
WhatsApp client is ready!
Server running on port 3000
```

You're now ready to interact with your AI-powered WhatsApp agent! 🤖✨
