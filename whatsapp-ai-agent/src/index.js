const express = require('express');
const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const { OpenAI } = require('openai');
require('dotenv').config();

// Import handlers (we'll create these next)
const ShoppingHandler = require('./handlers/shopping');
const AssignmentHandler = require('./handlers/assignment');
const YouTubeHandler = require('./handlers/youtube');
const FlightHandler = require('./handlers/flight');
const HotelHandler = require('./handlers/hotel');

// Initialize services
const app = express();
app.use(express.json());

// Debug: Check if API key is loaded
console.log('🔑 OpenAI API Key loaded:', process.env.OPENAI_API_KEY ? 'YES' : 'NO');
console.log('🔑 API Key starts with:', process.env.OPENAI_API_KEY ? process.env.OPENAI_API_KEY.substring(0, 10) + '...' : 'NOT FOUND');

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
});

// Initialize WhatsApp client
const whatsapp = new Client({
    authStrategy: new LocalAuth(),
    puppeteer: {
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu'
        ]
    }
});

// Session management
const userSessions = new Map();

// Initialize handlers
const handlers = {
    shopping: new ShoppingHandler(openai, userSessions),
    assignment: new AssignmentHandler(openai),
    youtube: new YouTubeHandler(openai),
    flight: new FlightHandler(openai, userSessions),
    hotel: new HotelHandler(openai, userSessions)
};

// WhatsApp event handlers
whatsapp.on('qr', qr => {
    console.log('QR Code received, scan with WhatsApp:');
    qrcode.generate(qr, { small: true });
});

whatsapp.on('ready', () => {
    console.log('🎉 WhatsApp client is ready!');
    console.log('📱 Waiting for messages...');
    console.log('💡 Send "hello" to yourself to test!');
});

whatsapp.on('message', async message => {
    try {
        console.log(`\n🚨 ===== MESSAGE DETECTED ===== 🚨`);
        console.log(`📨 NEW MESSAGE RECEIVED!`);
        console.log(`From: ${message.from}`);
        console.log(`Body: ${message.body}`);
        console.log(`Type: ${message.type}`);
        console.log(`Has Media: ${message.hasMedia}`);
        console.log(`🚨 ========================== 🚨\n`);

        // Detect intent
        const intent = await detectIntent(message.body);
        console.log(`🎯 Detected intent: ${intent.type}`);

        // Route to appropriate handler
        switch (intent.type) {
            case 'shopping':
                await handlers.shopping.handle(message);
                break;
            case 'youtube':
                await handlers.youtube.handle(message);
                break;
            case 'flight':
                await handlers.flight.handle(message);
                break;
            case 'hotel':
                await handlers.hotel.handle(message);
                break;
            default:
                if (message.hasMedia) {
                    await handlers.assignment.handle(message);
                } else {
                    await message.reply(
                        'Hi! I can help you with:\n\n' +
                        '🛍️ Shopping - "Find iPhone under $800"\n' +
                        '✈️ Flights - "Book flight NYC to London Dec 15"\n' +
                        '🏨 Hotels - "Find hotels in Paris"\n' +
                        '📚 Assignments - Send me a photo\n' +
                        '📺 YouTube - "Summarize [YouTube URL]"\n\n' +
                        'What would you like help with?'
                    );
                }
        }
    } catch (error) {
        console.error('Error processing message:', error);
        await message.reply('Sorry, I encountered an error. Please try again.');
    }
});

// Simple intent detection
async function detectIntent(text) {
    const lowercaseText = text.toLowerCase();

    if (lowercaseText.includes('buy') || lowercaseText.includes('shop') || lowercaseText.includes('order')) {
        return { type: 'shopping' };
    } else if (lowercaseText.includes('youtube') || lowercaseText.includes('summarize')) {
        return { type: 'youtube' };
    } else if (lowercaseText.includes('flight') || lowercaseText.includes('fly')) {
        return { type: 'flight' };
    } else if (lowercaseText.includes('hotel') || lowercaseText.includes('stay')) {
        return { type: 'hotel' };
    }

    return { type: 'unknown' };
}

// Start Express server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});

// Initialize WhatsApp
whatsapp.initialize();
