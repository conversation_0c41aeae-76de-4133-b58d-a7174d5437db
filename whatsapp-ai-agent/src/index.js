const express = require('express');
const bodyParser = require('body-parser');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Import handlers
const shoppingHandler = require('./handlers/shopping');
const flightHandler = require('./handlers/flight');
const hotelHandler = require('./handlers/hotel');
const assignmentHandler = require('./handlers/assignment');
const youtubeHandler = require('./handlers/youtube');

// Import services
const advancedFeatures = require('./services/advanced-features');
const monitoring = require('./services/monitoring');

// Import utilities
const intentDetector = require('./utils/intent-detector');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Webhook verification
app.get('/webhook', (req, res) => {
    const VERIFY_TOKEN = process.env.VERIFY_TOKEN;
    const mode = req.query['hub.mode'];
    const token = req.query['hub.verify_token'];
    const challenge = req.query['hub.challenge'];

    if (mode && token) {
        if (mode === 'subscribe' && token === VERIFY_TOKEN) {
            console.log('WEBHOOK_VERIFIED');
            res.status(200).send(challenge);
        } else {
            res.sendStatus(403);
        }
    }
});

// Webhook handler
app.post('/webhook', async (req, res) => {
    try {
        const body = req.body;

        if (body.object === 'whatsapp_business_account') {
            body.entry.forEach(async (entry) => {
                const changes = entry.changes;
                
                changes.forEach(async (change) => {
                    if (change.field === 'messages') {
                        const messages = change.value.messages;
                        
                        if (messages) {
                            for (const message of messages) {
                                await processMessage(message, change.value);
                            }
                        }
                    }
                });
            });
        }

        res.status(200).send('EVENT_RECEIVED');
    } catch (error) {
        console.error('Error processing webhook:', error);
        monitoring.logError('webhook_processing', error);
        res.status(500).send('Internal Server Error');
    }
});

// Process incoming messages
async function processMessage(message, value) {
    try {
        const phoneNumberId = value.metadata.phone_number_id;
        const from = message.from;
        const messageBody = message.text?.body || '';
        const messageType = message.type;

        // Log the message
        monitoring.logMessage(from, messageBody, messageType);

        // Detect intent
        const intent = await intentDetector.detectIntent(messageBody);
        
        console.log(`Detected intent: ${intent} for message: ${messageBody}`);

        // Route to appropriate handler based on intent
        let response;
        switch (intent) {
            case 'shopping':
                response = await shoppingHandler.handle(messageBody, from);
                break;
            case 'flight':
                response = await flightHandler.handle(messageBody, from);
                break;
            case 'hotel':
                response = await hotelHandler.handle(messageBody, from);
                break;
            case 'assignment':
                response = await assignmentHandler.handle(messageBody, from, message);
                break;
            case 'youtube':
                response = await youtubeHandler.handle(messageBody, from);
                break;
            default:
                response = await advancedFeatures.handleGeneral(messageBody, from);
        }

        // Send response
        if (response) {
            await sendMessage(phoneNumberId, from, response);
        }

    } catch (error) {
        console.error('Error processing message:', error);
        monitoring.logError('message_processing', error);
        
        // Send error message to user
        const errorResponse = "Sorry, I encountered an error processing your request. Please try again.";
        await sendMessage(value.metadata.phone_number_id, message.from, errorResponse);
    }
}

// Send message function
async function sendMessage(phoneNumberId, to, message) {
    try {
        const accessToken = process.env.WHATSAPP_ACCESS_TOKEN;
        const url = `https://graph.facebook.com/v18.0/${phoneNumberId}/messages`;

        const data = {
            messaging_product: 'whatsapp',
            to: to,
            text: { body: message }
        };

        const response = await axios.post(url, data, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('Message sent successfully:', response.data);
        monitoring.logOutgoingMessage(to, message);

    } catch (error) {
        console.error('Error sending message:', error.response?.data || error.message);
        monitoring.logError('message_sending', error);
    }
}

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`WhatsApp AI Agent server is running on port ${PORT}`);
    monitoring.logSystemEvent('server_started', { port: PORT });
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    monitoring.logSystemEvent('server_shutdown', { reason: 'SIGTERM' });
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    monitoring.logSystemEvent('server_shutdown', { reason: 'SIGINT' });
    process.exit(0);
});
