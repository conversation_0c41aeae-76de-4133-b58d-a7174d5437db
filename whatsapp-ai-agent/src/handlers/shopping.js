const axios = require('axios');
const monitoring = require('../services/monitoring');

class ShoppingHandler {
    constructor() {
        this.apiKey = process.env.SHOPPING_API_KEY;
        this.baseUrl = process.env.SHOPPING_API_URL || 'https://api.shopping-service.com';
    }

    async handle(message, userId) {
        try {
            console.log(`Processing shopping request from ${userId}: ${message}`);
            
            // Extract shopping intent and parameters
            const shoppingIntent = await this.parseShoppingIntent(message);
            
            switch (shoppingIntent.type) {
                case 'search':
                    return await this.searchProducts(shoppingIntent.query, userId);
                case 'compare':
                    return await this.compareProducts(shoppingIntent.products, userId);
                case 'price_alert':
                    return await this.setPriceAlert(shoppingIntent.product, shoppingIntent.price, userId);
                case 'recommendations':
                    return await this.getRecommendations(shoppingIntent.category, userId);
                case 'deals':
                    return await this.getDeals(shoppingIntent.category, userId);
                default:
                    return await this.getShoppingHelp();
            }
        } catch (error) {
            console.error('Error in shopping handler:', error);
            monitoring.logError('shopping_handler', error);
            return "Sorry, I couldn't process your shopping request. Please try again.";
        }
    }

    async parseShoppingIntent(message) {
        const lowerMessage = message.toLowerCase();
        
        // Search patterns
        if (lowerMessage.includes('search') || lowerMessage.includes('find') || lowerMessage.includes('look for')) {
            const query = this.extractSearchQuery(message);
            return { type: 'search', query };
        }
        
        // Compare patterns
        if (lowerMessage.includes('compare') || lowerMessage.includes('vs') || lowerMessage.includes('versus')) {
            const products = this.extractProductsToCompare(message);
            return { type: 'compare', products };
        }
        
        // Price alert patterns
        if (lowerMessage.includes('price alert') || lowerMessage.includes('notify when') || lowerMessage.includes('alert me')) {
            const { product, price } = this.extractPriceAlertInfo(message);
            return { type: 'price_alert', product, price };
        }
        
        // Recommendations patterns
        if (lowerMessage.includes('recommend') || lowerMessage.includes('suggest') || lowerMessage.includes('best')) {
            const category = this.extractCategory(message);
            return { type: 'recommendations', category };
        }
        
        // Deals patterns
        if (lowerMessage.includes('deal') || lowerMessage.includes('discount') || lowerMessage.includes('sale')) {
            const category = this.extractCategory(message);
            return { type: 'deals', category };
        }
        
        // Default to search
        return { type: 'search', query: message };
    }

    async searchProducts(query, userId) {
        try {
            const response = await axios.get(`${this.baseUrl}/search`, {
                params: {
                    q: query,
                    limit: 5,
                    user_id: userId
                },
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                }
            });

            const products = response.data.products;
            
            if (!products || products.length === 0) {
                return `Sorry, I couldn't find any products matching "${query}". Try a different search term.`;
            }

            let result = `🛍️ Found ${products.length} products for "${query}":\n\n`;
            
            products.forEach((product, index) => {
                result += `${index + 1}. *${product.name}*\n`;
                result += `   💰 Price: $${product.price}\n`;
                result += `   ⭐ Rating: ${product.rating}/5 (${product.reviews} reviews)\n`;
                result += `   🏪 Store: ${product.store}\n`;
                if (product.discount) {
                    result += `   🏷️ Discount: ${product.discount}% off\n`;
                }
                result += `   🔗 Link: ${product.url}\n\n`;
            });

            result += "Would you like me to compare any of these products or set up a price alert?";
            
            monitoring.logShoppingActivity(userId, 'search', { query, results: products.length });
            return result;

        } catch (error) {
            console.error('Error searching products:', error);
            return "Sorry, I couldn't search for products right now. Please try again later.";
        }
    }

    async compareProducts(products, userId) {
        try {
            if (!products || products.length < 2) {
                return "Please specify at least 2 products to compare. For example: 'Compare iPhone 15 vs Samsung Galaxy S24'";
            }

            const comparisons = [];
            for (const product of products) {
                const response = await axios.get(`${this.baseUrl}/product`, {
                    params: { name: product },
                    headers: { 'Authorization': `Bearer ${this.apiKey}` }
                });
                comparisons.push(response.data);
            }

            let result = "📊 *Product Comparison*\n\n";
            
            comparisons.forEach((product, index) => {
                result += `*${index + 1}. ${product.name}*\n`;
                result += `💰 Price: $${product.price}\n`;
                result += `⭐ Rating: ${product.rating}/5\n`;
                result += `📱 Features: ${product.features.join(', ')}\n`;
                result += `🏪 Available at: ${product.stores.join(', ')}\n\n`;
            });

            // Add comparison summary
            const cheapest = comparisons.reduce((min, p) => p.price < min.price ? p : min);
            const highest_rated = comparisons.reduce((max, p) => p.rating > max.rating ? p : max);

            result += "📈 *Summary:*\n";
            result += `💰 Best Price: ${cheapest.name} ($${cheapest.price})\n`;
            result += `⭐ Highest Rated: ${highest_rated.name} (${highest_rated.rating}/5)\n`;

            monitoring.logShoppingActivity(userId, 'compare', { products });
            return result;

        } catch (error) {
            console.error('Error comparing products:', error);
            return "Sorry, I couldn't compare those products. Please try again.";
        }
    }

    async setPriceAlert(product, targetPrice, userId) {
        try {
            const response = await axios.post(`${this.baseUrl}/price-alerts`, {
                user_id: userId,
                product_name: product,
                target_price: targetPrice,
                phone_number: userId
            }, {
                headers: { 'Authorization': `Bearer ${this.apiKey}` }
            });

            monitoring.logShoppingActivity(userId, 'price_alert', { product, targetPrice });
            
            return `🔔 Price alert set successfully!\n\nI'll notify you when "${product}" drops to $${targetPrice} or below.\n\nYou can manage your alerts by typing "my alerts".`;

        } catch (error) {
            console.error('Error setting price alert:', error);
            return "Sorry, I couldn't set up the price alert. Please try again.";
        }
    }

    async getRecommendations(category, userId) {
        try {
            const response = await axios.get(`${this.baseUrl}/recommendations`, {
                params: {
                    category: category || 'general',
                    user_id: userId,
                    limit: 5
                },
                headers: { 'Authorization': `Bearer ${this.apiKey}` }
            });

            const recommendations = response.data.recommendations;
            
            let result = `🎯 *Recommended ${category || 'Products'} for You:*\n\n`;
            
            recommendations.forEach((product, index) => {
                result += `${index + 1}. *${product.name}*\n`;
                result += `   💰 $${product.price}\n`;
                result += `   ⭐ ${product.rating}/5\n`;
                result += `   📝 ${product.reason}\n\n`;
            });

            monitoring.logShoppingActivity(userId, 'recommendations', { category });
            return result;

        } catch (error) {
            console.error('Error getting recommendations:', error);
            return "Sorry, I couldn't get recommendations right now. Please try again later.";
        }
    }

    async getDeals(category, userId) {
        try {
            const response = await axios.get(`${this.baseUrl}/deals`, {
                params: {
                    category: category || 'all',
                    limit: 5
                },
                headers: { 'Authorization': `Bearer ${this.apiKey}` }
            });

            const deals = response.data.deals;
            
            let result = `🔥 *Hot Deals${category ? ` in ${category}` : ''}:*\n\n`;
            
            deals.forEach((deal, index) => {
                result += `${index + 1}. *${deal.product_name}*\n`;
                result += `   💰 Was: $${deal.original_price} → Now: $${deal.sale_price}\n`;
                result += `   🏷️ Save: ${deal.discount_percentage}% (${deal.savings})\n`;
                result += `   ⏰ Ends: ${deal.end_date}\n`;
                result += `   🏪 ${deal.store}\n\n`;
            });

            monitoring.logShoppingActivity(userId, 'deals', { category });
            return result;

        } catch (error) {
            console.error('Error getting deals:', error);
            return "Sorry, I couldn't get current deals. Please try again later.";
        }
    }

    extractSearchQuery(message) {
        const patterns = [
            /search for (.+)/i,
            /find (.+)/i,
            /look for (.+)/i,
            /looking for (.+)/i
        ];
        
        for (const pattern of patterns) {
            const match = message.match(pattern);
            if (match) return match[1].trim();
        }
        
        return message.trim();
    }

    extractProductsToCompare(message) {
        const vsPattern = /(.+?)\s+(?:vs|versus)\s+(.+)/i;
        const match = message.match(vsPattern);
        
        if (match) {
            return [match[1].trim(), match[2].trim()];
        }
        
        // Fallback: split by common separators
        const separators = [' and ', ' & ', ','];
        for (const sep of separators) {
            if (message.includes(sep)) {
                return message.split(sep).map(p => p.trim());
            }
        }
        
        return [message.trim()];
    }

    extractPriceAlertInfo(message) {
        const pricePattern = /\$?(\d+(?:\.\d{2})?)/;
        const priceMatch = message.match(pricePattern);
        const price = priceMatch ? parseFloat(priceMatch[1]) : null;
        
        const product = message.replace(/price alert|notify when|alert me|when|drops to|\$\d+(?:\.\d{2})?/gi, '').trim();
        
        return { product, price };
    }

    extractCategory(message) {
        const categories = ['electronics', 'clothing', 'books', 'home', 'sports', 'beauty', 'toys', 'automotive'];
        const lowerMessage = message.toLowerCase();
        
        for (const category of categories) {
            if (lowerMessage.includes(category)) {
                return category;
            }
        }
        
        return null;
    }

    async getShoppingHelp() {
        return `🛍️ *Shopping Assistant Help*\n\nI can help you with:\n\n` +
               `🔍 *Search Products*\n"Search for iPhone 15" or "Find wireless headphones"\n\n` +
               `📊 *Compare Products*\n"Compare iPhone 15 vs Samsung Galaxy S24"\n\n` +
               `🔔 *Price Alerts*\n"Alert me when iPhone 15 drops to $800"\n\n` +
               `🎯 *Recommendations*\n"Recommend laptops" or "Best smartphones"\n\n` +
               `🔥 *Current Deals*\n"Show deals in electronics" or "Current sales"\n\n` +
               `Just type what you're looking for and I'll help you find the best products and deals!`;
    }
}

module.exports = new ShoppingHandler();
