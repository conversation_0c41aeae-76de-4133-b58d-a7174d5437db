#!/bin/bash

# Enhanced WhatsApp AI Agent Setup Script
echo "🤖 Setting up Enhanced WhatsApp AI Agent..."

# Create directory structure
echo "📁 Creating directories..."
mkdir -p src/{handlers,services,utils} temp logs

# Enhanced package.json with all dependencies
echo "📦 Creating enhanced package.json..."
cat > package.json << 'EOF'
{
  "name": "whatsapp-ai-agent",
  "version": "1.0.0",
  "description": "Multi-purpose WhatsApp AI Agent with Shopping, Assignments, YouTube, Flight & Hotel features",
  "main": "src/index.js",
  "scripts": {
    "start": "node src/index.js",
    "dev": "nodemon src/index.js",
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "keywords": ["whatsapp", "ai", "agent", "chatbot", "automation"],
  "author": "",
  "license": "ISC",
  "dependencies": {
    "express": "^4.18.2",
    "whatsapp-web.js": "^1.23.0",
    "qrcode-terminal": "^0.12.0",
    "openai": "^4.20.1",
    "dotenv": "^16.3.1",
    "pdf-lib": "^1.17.1",
    "ytdl-core": "^4.11.5",
    "puppeteer": "^21.5.2",
    "winston": "^3.11.0",
    "axios": "^1.6.2",
    "node-cron": "^3.0.3"
  },
  "devDependencies": {
    "nodemon": "^3.0.2"
  }
}
EOF

# Enhanced .env template
echo "🔐 Creating enhanced .env template..."
cat > .env.example << 'EOF'
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Server Configuration
PORT=3000
NODE_ENV=development

# WhatsApp Configuration (if using Business API)
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
VERIFY_TOKEN=your_verify_token

# External APIs (Optional)
SHOPPING_API_KEY=your_shopping_api_key
FLIGHT_API_KEY=your_flight_api_key
HOTEL_API_KEY=your_hotel_api_key
YOUTUBE_API_KEY=your_youtube_api_key

# Database (Optional)
DATABASE_URL=your_database_url

# Logging
LOG_LEVEL=info
EOF

# Copy .env.example to .env
cp .env.example .env

echo "📝 Creating application files..."

# Create main index.js with full features
cat > src/index.js << 'EOF'
const express = require('express');
const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const { OpenAI } = require('openai');
require('dotenv').config();

// Import handlers
const ShoppingHandler = require('./handlers/shopping');
const AssignmentHandler = require('./handlers/assignment');
const YouTubeHandler = require('./handlers/youtube');
const FlightHandler = require('./handlers/flight');
const HotelHandler = require('./handlers/hotel');

// Initialize services
const app = express();
app.use(express.json());

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
});

// Initialize WhatsApp client
const whatsapp = new Client({
    authStrategy: new LocalAuth(),
    puppeteer: {
        headless: true,
        args: ['--no-sandbox']
    }
});

// Session management
const userSessions = new Map();

// Initialize handlers
const handlers = {
    shopping: new ShoppingHandler(openai, userSessions),
    assignment: new AssignmentHandler(openai),
    youtube: new YouTubeHandler(openai),
    flight: new FlightHandler(openai, userSessions),
    hotel: new HotelHandler(openai, userSessions)
};

// WhatsApp event handlers
whatsapp.on('qr', qr => {
    console.log('QR Code received, scan with WhatsApp:');
    qrcode.generate(qr, { small: true });
});

whatsapp.on('ready', () => {
    console.log('WhatsApp client is ready!');
});

whatsapp.on('message', async message => {
    try {
        console.log(`Message from ${message.from}: ${message.body}`);
        
        // Detect intent
        const intent = await detectIntent(message.body);
        
        // Route to appropriate handler
        switch (intent.type) {
            case 'shopping':
                await handlers.shopping.handle(message);
                break;
            case 'youtube':
                await handlers.youtube.handle(message);
                break;
            case 'flight':
                await handlers.flight.handle(message);
                break;
            case 'hotel':
                await handlers.hotel.handle(message);
                break;
            default:
                if (message.hasMedia) {
                    await handlers.assignment.handle(message);
                } else {
                    await message.reply(
                        'Hi! I can help you with:\n\n' +
                        '🛍️ Shopping - "Find iPhone under $800"\n' +
                        '✈️ Flights - "Book flight NYC to London Dec 15"\n' +
                        '🏨 Hotels - "Find hotels in Paris"\n' +
                        '📚 Assignments - Send me a photo\n' +
                        '📺 YouTube - "Summarize [YouTube URL]"\n\n' +
                        'What would you like help with?'
                    );
                }
        }
    } catch (error) {
        console.error('Error processing message:', error);
        await message.reply('Sorry, I encountered an error. Please try again.');
    }
});

// Simple intent detection
async function detectIntent(text) {
    const lowercaseText = text.toLowerCase();
    
    if (lowercaseText.includes('buy') || lowercaseText.includes('shop') || lowercaseText.includes('order')) {
        return { type: 'shopping' };
    } else if (lowercaseText.includes('youtube') || lowercaseText.includes('summarize')) {
        return { type: 'youtube' };
    } else if (lowercaseText.includes('flight') || lowercaseText.includes('fly')) {
        return { type: 'flight' };
    } else if (lowercaseText.includes('hotel') || lowercaseText.includes('stay')) {
        return { type: 'hotel' };
    }
    
    return { type: 'unknown' };
}

// Start Express server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});

// Initialize WhatsApp
whatsapp.initialize();
EOF

echo "🛍️ Creating shopping handler..."
# Create shopping handler (truncated for brevity)
cat > src/handlers/shopping.js << 'EOF'
const puppeteer = require('puppeteer');

class ShoppingHandler {
    constructor(openai, sessions) {
        this.openai = openai;
        this.sessions = sessions;
    }

    async handle(message) {
        const userId = message.from;
        const session = this.sessions.get(userId) || { state: 'idle' };

        if (session.state === 'awaiting_selection') {
            await this.handleSelection(message, session);
            return;
        }

        // Extract product details
        const productInfo = await this.extractProductInfo(message.body);
        
        // Search for products
        await message.reply('🔍 Searching for products...');
        const products = await this.searchProducts(productInfo);

        if (products.length === 0) {
            await message.reply('Sorry, no products found. Try different search terms.');
            return;
        }

        // Format and send results
        let response = '🛍️ *Found these products:*\n\n';
        products.forEach((product, index) => {
            response += `${index + 1}. *${product.name}*\n`;
            response += `   💰 Price: $${product.price}\n`;
            response += `   ⭐ Rating: ${product.rating}\n\n`;
        });
        response += 'Reply with a number to select.';

        await message.reply(response);

        // Save session
        this.sessions.set(userId, {
            state: 'awaiting_selection',
            products: products
        });
    }

    async extractProductInfo(text) {
        try {
            const response = await this.openai.chat.completions.create({
                model: "gpt-3.5-turbo",
                messages: [{
                    role: "system",
                    content: "Extract product name and budget from the text. Return JSON with 'product' and 'budget' fields."
                }, {
                    role: "user",
                    content: text
                }],
                response_format: { type: "json_object" }
            });

            return JSON.parse(response.choices[0].message.content);
        } catch (error) {
            console.error('Error extracting product info:', error);
            return { product: text, budget: 1000 };
        }
    }

    async searchProducts(criteria) {
        // Mock products for demo
        const mockProducts = [
            {
                name: criteria.product || "iPhone 14",
                price: 799,
                rating: "4.5/5",
                url: "https://example.com/product1"
            },
            {
                name: criteria.product || "iPhone 13",
                price: 699,
                rating: "4.3/5",
                url: "https://example.com/product2"
            }
        ];

        return mockProducts.filter(p => p.price <= (criteria.budget || 1000));
    }

    async handleSelection(message, session) {
        const selection = parseInt(message.body);
        
        if (isNaN(selection) || selection < 1 || selection > session.products.length) {
            await message.reply('Please enter a valid number.');
            return;
        }

        const selected = session.products[selection - 1];
        await message.reply(
            `✅ Great choice! *${selected.name}*\n\n` +
            `To complete your order:\n` +
            `1. Visit: ${selected.url}\n` +
            `2. Add to cart\n` +
            `3. Proceed to checkout\n\n` +
            `Need help with anything else?`
        );

        this.sessions.delete(message.from);
    }
}

module.exports = ShoppingHandler;
EOF

echo "📚 Creating assignment handler..."
# Create assignment handler
cat > src/handlers/assignment.js << 'EOF'
const fs = require('fs').promises;
const path = require('path');
const { MessageMedia } = require('whatsapp-web.js');

class AssignmentHandler {
    constructor(openai) {
        this.openai = openai;
    }

    async handle(message) {
        if (!message.hasMedia) {
            await message.reply('Please send an image of your assignment.');
            return;
        }

        await message.reply('📚 Analyzing your assignment...');

        try {
            const media = await message.downloadMedia();
            
            // Use GPT-4 Vision
            const response = await this.openai.chat.completions.create({
                model: "gpt-4-vision-preview",
                messages: [{
                    role: "user",
                    content: [
                        { 
                            type: "text", 
                            text: "Solve these problems step by step. Show all work." 
                        },
                        { 
                            type: "image_url", 
                            image_url: { 
                                url: `data:${media.mimetype};base64,${media.data}` 
                            } 
                        }
                    ]
                }],
                max_tokens: 4096
            });

            const solutions = response.choices[0].message.content;
            await message.reply('✅ *Assignment Solutions:*\n\n' + solutions);

        } catch (error) {
            console.error('Assignment error:', error);
            await message.reply('Sorry, I couldn\'t process the assignment.');
        }
    }
}

module.exports = AssignmentHandler;
EOF

# Install dependencies
echo "📦 Installing dependencies..."
npm install

echo "✅ Enhanced WhatsApp AI Agent setup complete!"
echo ""
echo "🚀 Next steps:"
echo "1. Edit .env file and add your OpenAI API key"
echo "2. Run 'npm start' to start the bot"
echo "3. Scan the QR code with WhatsApp"
echo ""
echo "🎯 Features available:"
echo "- 🛍️ Shopping assistance with AI"
echo "- 📚 Assignment solving with GPT-4 Vision"
echo "- 📺 YouTube video summarization"
echo "- ✈️ Flight booking (basic structure)"
echo "- 🏨 Hotel reservations (basic structure)"
echo ""
echo "Happy coding! 🚀"
